<!DOCTYPE html>
<html>
<head>
    <title>Multi Key Processing Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>WhatsApp Automation - Multi Key Processing Test</h1>
    
    <div class="test-section">
        <h2>Test Sample API Response</h2>
        <p>This test validates the multi key processing functionality with sample data.</p>
        <button onclick="testMultiKeyProcessing()">Run Multi Key Test</button>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>Sample API Response Structure</h2>
        <pre id="sampleData"></pre>
    </div>

    <div class="test-section">
        <h2>Expected Processing Results</h2>
        <div id="expectedResults">
            <h3>For Message ID 170006:</h3>
            <ul>
                <li><strong>Phone:</strong> 919703924689</li>
                <li><strong>Multi Files:</strong> 5 total (3 cloud, 2 local)</li>
                <li><strong>Cloud Files:</strong>
                    <ul>
                        <li>human-body-organs-internal-parts-600nw-2426287857.jpg (3 instances)</li>
                    </ul>
                </li>
                <li><strong>Local Files:</strong>
                    <ul>
                        <li>1.jpeg</li>
                        <li>2.jpeg</li>
                    </ul>
                </li>
            </ul>
            
            <h3>For Message ID 170007:</h3>
            <ul>
                <li><strong>Phone:</strong> 919876543210</li>
                <li><strong>Multi Files:</strong> 2 total (1 cloud, 1 local)</li>
                <li><strong>Cloud Files:</strong>
                    <ul>
                        <li>dummy.pdf</li>
                    </ul>
                </li>
                <li><strong>Local Files:</strong>
                    <ul>
                        <li>document.pdf</li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>

    <script>
        // Sample API response data
        const sampleApiResponse = {
            "success": true,
            "whatsapp": [
                {
                    "id": "170006",
                    "user_id": "9cb65fdf72-36",
                    "mobile": "919703924689",
                    "message": "Testing message from API",
                    "caption": "Testing caption text",
                    "status": "0",
                    "link": "https://www.shutterstock.com/image-vector/moscow-russia-august-18-2021-260nw-2300430413.jpg",
                    "multi": "{\"success\":true,\"whatsapp\":[{\"message\":\"Cloud image message 1\",\"caption\":\"Caption for first cloud image\",\"status\":\"1\",\"link\":\"https://www.shutterstock.com/image-illustration/human-body-organs-internal-parts-600nw-2426287857.jpg\"},{\"message\":\"Cloud image message 2\",\"caption\":\"Caption for second cloud image\",\"status\":\"1\",\"link\":\"https://www.shutterstock.com/image-illustration/human-body-organs-internal-parts-600nw-2426287857.jpg\"},{\"message\":\"Cloud image message 3\",\"caption\":\"Caption for third cloud image\",\"status\":\"1\",\"link\":\"https://www.shutterstock.com/image-illustration/human-body-organs-internal-parts-600nw-2426287857.jpg\"},{\"message\":\"Local file message 1\",\"caption\":\"Caption for first local image\",\"status\":\"1\",\"link\":\"1.jpeg\"},{\"message\":\"Local file message 2\",\"caption\":\"Caption for second local image\",\"status\":\"1\",\"link\":\"2.jpeg\"}]}"
                },
                {
                    "id": "170007",
                    "user_id": "9cb65fdf72-36",
                    "mobile": "919876543210",
                    "message": "Another test message",
                    "caption": "Another caption",
                    "status": "0",
                    "link": "",
                    "multi": "{\"success\":true,\"whatsapp\":[{\"message\":\"Document message\",\"caption\":\"Important document\",\"status\":\"1\",\"link\":\"https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf\"},{\"message\":\"Local document\",\"caption\":\"Local PDF file\",\"status\":\"1\",\"link\":\"document.pdf\"}]}"
                }
            ]
        };

        // Display sample data
        document.getElementById('sampleData').textContent = JSON.stringify(sampleApiResponse, null, 2);

        // Simulate the multi key processing function
        function processQueueWithMultiKey(queueData) {
            console.log("Processing queue with multi key data...");
            const processedQueue = [];

            for (const item of queueData) {
                console.log("Processing queue item:", item);
                
                const processedItem = {
                    id: item.id,
                    user_id: item.user_id,
                    mobile: item.mobile,
                    message: item.message || "",
                    caption: item.caption || "",
                    status: item.status || "0",
                    link: item.link || "",
                    multi: item.multi || "",
                    processedFiles: []
                };

                // Process multi key if it exists
                if (item.multi && item.multi.trim()) {
                    try {
                        console.log("Processing multi key for item:", item.id);
                        const multiData = JSON.parse(item.multi);
                        
                        if (multiData.success && Array.isArray(multiData.whatsapp)) {
                            console.log(`Found ${multiData.whatsapp.length} items in multi key`);
                            
                            for (const multiItem of multiData.whatsapp) {
                                const fileItem = {
                                    message: multiItem.message || "",
                                    caption: multiItem.caption || "",
                                    status: multiItem.status || "1",
                                    link: multiItem.link || "",
                                    isLocal: false,
                                    fileData: null,
                                    error: null
                                };

                                // Determine if this is a local file or cloud file
                                if (multiItem.link && !multiItem.link.startsWith('http')) {
                                    // Local file
                                    fileItem.isLocal = true;
                                    fileItem.localFileName = multiItem.link;
                                    console.log(`Local file detected: ${multiItem.link}`);
                                    
                                } else if (multiItem.link && multiItem.link.startsWith('http')) {
                                    // Cloud file
                                    fileItem.isLocal = false;
                                    console.log(`Cloud file detected: ${multiItem.link}`);
                                }

                                processedItem.processedFiles.push(fileItem);
                            }
                        }
                    } catch (multiError) {
                        console.error("Error processing multi key for item:", item.id, multiError);
                        processedItem.multiError = multiError.message;
                    }
                }

                processedQueue.push(processedItem);
            }

            console.log(`Processed ${processedQueue.length} queue items with multi key data`);
            return processedQueue;
        }

        function testMultiKeyProcessing() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<p>Running test...</p>';

            try {
                // Process the sample data
                const processedQueue = processQueueWithMultiKey(sampleApiResponse.whatsapp);
                
                // Generate test results
                let resultsHtml = '<div class="success"><h3>✅ Test Passed!</h3>';
                
                processedQueue.forEach((item, index) => {
                    const localFiles = item.processedFiles.filter(f => f.isLocal);
                    const cloudFiles = item.processedFiles.filter(f => !f.isLocal);
                    
                    resultsHtml += `
                        <h4>Message ${index + 1} (ID: ${item.id})</h4>
                        <ul>
                            <li><strong>Phone:</strong> ${item.mobile}</li>
                            <li><strong>Total Multi Files:</strong> ${item.processedFiles.length}</li>
                            <li><strong>Local Files:</strong> ${localFiles.length} (${localFiles.map(f => f.localFileName).join(', ')})</li>
                            <li><strong>Cloud Files:</strong> ${cloudFiles.length} (${cloudFiles.map(f => f.link.split('/').pop()).join(', ')})</li>
                        </ul>
                    `;
                });
                
                resultsHtml += '</div>';
                resultsDiv.innerHTML = resultsHtml;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error"><h3>❌ Test Failed!</h3><p>Error: ${error.message}</p></div>`;
            }
        }

        // Auto-run test on page load
        window.onload = function() {
            testMultiKeyProcessing();
        };
    </script>
</body>
</html>
