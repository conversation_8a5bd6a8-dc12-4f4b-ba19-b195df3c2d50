# WhatsApp Automation Extension with Multi Key Support

This Chrome extension automates WhatsApp message sending with support for processing multi key data from API responses, handling both cloud and local files.

## Features

- **Multi Key Processing**: Processes the `multi` key from API responses containing JSON data with multiple files
- **Cloud File Support**: Downloads and sends files from HTTP/HTTPS URLs
- **Local File Support**: References local files from a specified folder path
- **Bulk Messaging**: Send messages to multiple recipients from API queue
- **File Attachments**: Support for images, videos, documents, and audio files
- **Caption Support**: Individual captions for each file from multi key data

## Setup Instructions

1. **Install the Extension**:
   - Load the extension in Chrome Developer Mode
   - Navigate to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked" and select this folder

2. **Configure API Settings**:
   - Open the extension popup
   - Go to the "API Bulk" tab
   - Enter your API URL, User ID, and Secret Key
   - Set the Local Files Folder Path (e.g., `C:\Users\<USER>\Documents\WhatsAppFiles`)

3. **Local Folder Setup**:
   - Create a folder on your computer for local files
   - Place your local files (referenced in API multi key) in this folder
   - Example path: `C:\Users\<USER>\Documents\WhatsAppFiles`
   - Files like `1.jpeg`, `2.jpeg`, `document.pdf` should be in this folder

## API Response Format

Your API should return data in this format:

```json
{
  "success": true,
  "whatsapp": [
    {
      "id": "170006",
      "user_id": "9cb65fdf72-36",
      "mobile": "919703924689",
      "message": "Main message text",
      "caption": "Main caption text",
      "status": "0",
      "link": "https://example.com/main-image.jpg",
      "multi": "{\"success\":true,\"whatsapp\":[{\"message\":\"Cloud file message\",\"caption\":\"Caption for cloud image\",\"status\":\"1\",\"link\":\"https://example.com/cloud-image.jpg\"},{\"message\":\"Local file message\",\"caption\":\"Caption for local image\",\"status\":\"1\",\"link\":\"local-image.jpeg\"}]}"
    }
  ]
}
```

## Multi Key Processing

The extension processes the `multi` key as follows:

1. **Parses the JSON string** in the `multi` field
2. **Identifies file types**:
   - **Cloud files**: URLs starting with `http://` or `https://`
   - **Local files**: Filenames without HTTP protocol (e.g., `image.jpg`, `document.pdf`)
3. **Downloads cloud files** automatically during message sending
4. **References local files** from the configured folder path
5. **Sends each file** with its individual caption and message

## Usage

1. **Test API Connection**:
   - Click "Test API Connection" to verify your API setup
   - Check the debug information for response details

2. **Fetch Message Queue**:
   - Click "Fetch Message Queue" to load messages from your API
   - The extension will process multi key data automatically
   - View the queue to see cloud and local file counts

3. **Start Bulk Sending**:
   - Set the delay between messages (recommended: 10+ seconds)
   - Click "Start Bulk Send" to begin processing
   - Monitor progress in the progress bar

## File Type Support

- **Images**: JPG, PNG, GIF, WebP
- **Videos**: MP4, AVI, MOV, WebM
- **Documents**: PDF, DOC, DOCX, TXT
- **Audio**: MP3, WAV, OGG
- **Archives**: ZIP, RAR

## Local File Path Examples

- **Windows**: `C:\Users\<USER>\Documents\WhatsAppFiles`
- **macOS**: `/Users/<USER>/Documents/WhatsAppFiles`
- **Linux**: `/home/<USER>/Documents/WhatsAppFiles`

## Troubleshooting

1. **Local Files Not Found**:
   - Verify the folder path is correct
   - Ensure files exist in the specified folder
   - Check file permissions

2. **Cloud Files Not Downloading**:
   - Verify URLs are accessible
   - Check for CORS restrictions
   - Ensure stable internet connection

3. **API Connection Issues**:
   - Use "Test API Connection" to debug
   - Check API URL, User ID, and Secret Key
   - Verify API response format

## Security Notes

- Local file access is limited by browser security
- Extension requires appropriate permissions
- API credentials are stored locally in browser storage
- Files are processed in memory and not permanently stored

## Example Workflow

1. Set up your local files folder: `C:\Users\<USER>\Documents\WhatsAppFiles`
2. Place files like `1.jpeg`, `2.jpeg`, `document.pdf` in the folder
3. Configure API settings in the extension
4. Your API returns multi key data with both cloud URLs and local filenames
5. Extension downloads cloud files and references local files
6. Sends messages with appropriate files and captions to each recipient

This system allows you to mix cloud-hosted files with local files in a single API response, giving you flexibility in how you manage and distribute media content.
