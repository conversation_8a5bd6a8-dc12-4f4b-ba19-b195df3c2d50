// Enhanced background.js with file handling support

console.log("WhatsApp Automation Background Script v3.0 loaded")

const chrome = window.chrome

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log("Background received:", request.action, request)

  switch (request.action) {
    case "fetchQueue":
      handleFetchQueue(request, sendResponse)
      break
    case "directFetch":
      handleDirectFetch(request, sendResponse)
      break
    case "updateStatus":
      handleUpdateStatus(request, sendResponse)
      break
    case "contentScriptReady":
      console.log("Content script is ready")
      sendResponse({ success: true })
      break
    default:
      sendResponse({ success: false, error: "Unknown action" })
  }

  return true
})

async function handleDirectFetch(request, sendResponse) {
  try {
    const { url } = request

    if (!url) {
      throw new Error("Missing URL parameter")
    }

    console.log("Direct fetch URL:", url)

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "*/*",
        "Cache-Control": "no-cache",
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const text = await response.text()
    console.log("Direct fetch response:", text.substring(0, 200) + (text.length > 200 ? "..." : ""))

    try {
      const data = JSON.parse(text)
      sendResponse({ success: true, data: data, text: text })
    } catch (e) {
      sendResponse({ success: false, error: "Invalid JSON", text: text })
    }
  } catch (error) {
    console.error("Direct fetch error:", error)
    sendResponse({ success: false, error: error.message })
  }
}

async function handleFetchQueue(request, sendResponse) {
  try {
    const { apiUrl, userId, secret } = request

    if (!apiUrl || !userId || !secret) {
      throw new Error("Missing required parameters")
    }

    console.log("Fetching queue with params:", { apiUrl, userId })

    let url = apiUrl
    if (!url.includes("?")) {
      url += "?"
    } else if (!url.endsWith("&") && !url.endsWith("?")) {
      url += "&"
    }

    url += `method=list_whatsapp_l&userid=${encodeURIComponent(userId)}&secret=${encodeURIComponent(secret)}`
    console.log("Full API URL:", url)

    console.log("Starting fetch request...")
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "*/*",
        "Cache-Control": "no-cache",
      },
    })

    console.log("Fetch response status:", response.status, response.statusText)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const text = await response.text()
    console.log("Raw API Response (first 200 chars):", text.substring(0, 200) + (text.length > 200 ? "..." : ""))

    let data
    try {
      data = JSON.parse(text)
      console.log("Parsed API Response:", data)
    } catch (e) {
      console.error("JSON parse error:", e)
      throw new Error("Invalid JSON response from API: " + text.substring(0, 100))
    }

    if (!data) {
      throw new Error("Empty response from API")
    }

    console.log("Sending success response back to popup")
    sendResponse({ success: true, data: data, text: text })
  } catch (error) {
    console.error("Queue fetch failed:", error)
    sendResponse({ success: false, error: error.message })
  }
}

async function handleUpdateStatus(request, sendResponse) {
  try {
    const { apiUrl, userId, secret, id, status } = request

    if (!apiUrl || !userId || !secret || !id || !status) {
      throw new Error("Missing required parameters for status update")
    }

    const response = await updateMessageStatus(apiUrl, userId, secret, id, status)
    console.log("Status update successful:", response)
    sendResponse({ success: true, data: response })
  } catch (error) {
    console.error("Status update failed:", error)
    sendResponse({ success: false, error: error.message })
  }
}

async function updateMessageStatus(apiUrl, userId, secret, messageId, status) {
  let url = apiUrl
  if (!url.includes("?")) {
    url += "?"
  } else if (!url.endsWith("&") && !url.endsWith("?")) {
    url += "&"
  }

  url += `method=update_status&userid=${encodeURIComponent(userId)}&secret=${encodeURIComponent(secret)}&id=${encodeURIComponent(messageId)}&status=${encodeURIComponent(status)}`

  console.log("Updating status:", { messageId, status, url })

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "*/*",
        "Cache-Control": "no-cache",
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const text = await response.text()
    console.log("Raw status update response:", text)

    let data
    try {
      data = JSON.parse(text)
    } catch (e) {
      console.error("JSON parse error:", e)
      throw new Error("Invalid JSON response from API")
    }

    return data
  } catch (error) {
    if (error.name === "TypeError" && error.message.includes("fetch")) {
      throw new Error("Network error: Unable to update status")
    }
    throw error
  }
}

chrome.runtime.onStartup.addListener(() => {
  console.log("Extension started")
})

chrome.runtime.onInstalled.addListener((details) => {
  console.log("Extension installed/updated:", details.reason)
})
