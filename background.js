// Enhanced background.js with file handling support and multi key processing

console.log("WhatsApp Automation Background Script v4.0 loaded")

const chrome = window.chrome

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log("Background received:", request.action, request)

  switch (request.action) {
    case "fetchQueue":
      handleFetchQueue(request, sendResponse)
      break
    case "directFetch":
      handleDirectFetch(request, sendResponse)
      break
    case "updateStatus":
      handleUpdateStatus(request, sendResponse)
      break
    case "processMultiKey":
      handleProcessMultiKey(request, sendResponse)
      break
    case "downloadFile":
      handleDownloadFile(request, sendResponse)
      break
    case "readLocalFile":
      handleReadLocalFile(request, sendResponse)
      break
    case "contentScriptReady":
      console.log("Content script is ready")
      sendResponse({ success: true })
      break
    default:
      sendResponse({ success: false, error: "Unknown action" })
  }

  return true
})

async function handleDirectFetch(request, sendResponse) {
  try {
    const { url } = request

    if (!url) {
      throw new Error("Missing URL parameter")
    }

    console.log("Direct fetch URL:", url)

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "*/*",
        "Cache-Control": "no-cache",
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const text = await response.text()
    console.log("Direct fetch response:", text.substring(0, 200) + (text.length > 200 ? "..." : ""))

    try {
      const data = JSON.parse(text)
      sendResponse({ success: true, data: data, text: text })
    } catch (e) {
      sendResponse({ success: false, error: "Invalid JSON", text: text })
    }
  } catch (error) {
    console.error("Direct fetch error:", error)
    sendResponse({ success: false, error: error.message })
  }
}

async function handleFetchQueue(request, sendResponse) {
  try {
    const { apiUrl, userId, secret } = request

    if (!apiUrl || !userId || !secret) {
      throw new Error("Missing required parameters")
    }

    console.log("Fetching queue with params:", { apiUrl, userId })

    let url = apiUrl
    if (!url.includes("?")) {
      url += "?"
    } else if (!url.endsWith("&") && !url.endsWith("?")) {
      url += "&"
    }

    url += `method=list_whatsapp_l&userid=${encodeURIComponent(userId)}&secret=${encodeURIComponent(secret)}`
    console.log("Full API URL:", url)

    console.log("Starting fetch request...")
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "*/*",
        "Cache-Control": "no-cache",
      },
    })

    console.log("Fetch response status:", response.status, response.statusText)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const text = await response.text()
    console.log("Raw API Response (first 200 chars):", text.substring(0, 200) + (text.length > 200 ? "..." : ""))

    let data
    try {
      data = JSON.parse(text)
      console.log("Parsed API Response:", data)
    } catch (e) {
      console.error("JSON parse error:", e)
      throw new Error("Invalid JSON response from API: " + text.substring(0, 100))
    }

    if (!data) {
      throw new Error("Empty response from API")
    }

    console.log("Sending success response back to popup")
    sendResponse({ success: true, data: data, text: text })
  } catch (error) {
    console.error("Queue fetch failed:", error)
    sendResponse({ success: false, error: error.message })
  }
}

async function handleUpdateStatus(request, sendResponse) {
  try {
    const { apiUrl, userId, secret, id, status } = request

    if (!apiUrl || !userId || !secret || !id || !status) {
      throw new Error("Missing required parameters for status update")
    }

    const response = await updateMessageStatus(apiUrl, userId, secret, id, status)
    console.log("Status update successful:", response)
    sendResponse({ success: true, data: response })
  } catch (error) {
    console.error("Status update failed:", error)
    sendResponse({ success: false, error: error.message })
  }
}

async function updateMessageStatus(apiUrl, userId, secret, messageId, status) {
  let url = apiUrl
  if (!url.includes("?")) {
    url += "?"
  } else if (!url.endsWith("&") && !url.endsWith("?")) {
    url += "&"
  }

  url += `method=update_status&userid=${encodeURIComponent(userId)}&secret=${encodeURIComponent(secret)}&id=${encodeURIComponent(messageId)}&status=${encodeURIComponent(status)}`

  console.log("Updating status:", { messageId, status, url })

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "*/*",
        "Cache-Control": "no-cache",
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const text = await response.text()
    console.log("Raw status update response:", text)

    let data
    try {
      data = JSON.parse(text)
    } catch (e) {
      console.error("JSON parse error:", e)
      throw new Error("Invalid JSON response from API")
    }

    return data
  } catch (error) {
    if (error.name === "TypeError" && error.message.includes("fetch")) {
      throw new Error("Network error: Unable to update status")
    }
    throw error
  }
}

chrome.runtime.onStartup.addListener(() => {
  console.log("Extension started")
})

chrome.runtime.onInstalled.addListener((details) => {
  console.log("Extension installed/updated:", details.reason)
})

// Process multi key data from API response
async function handleProcessMultiKey(request, sendResponse) {
  try {
    const { multiKeyData } = request

    if (!multiKeyData) {
      throw new Error("Multi key data is required")
    }

    console.log("\n=== BACKGROUND: PROCESSING MULTI KEY DATA ===")
    console.log("Multi key data:", multiKeyData)

    let parsedData
    try {
      // Parse the multi key JSON string
      parsedData = typeof multiKeyData === 'string' ? JSON.parse(multiKeyData) : multiKeyData
    } catch (parseError) {
      throw new Error("Invalid JSON in multi key data: " + parseError.message)
    }

    if (!parsedData.success || !Array.isArray(parsedData.whatsapp)) {
      throw new Error("Invalid multi key data structure")
    }

    const processedItems = []

    for (const item of parsedData.whatsapp) {
      console.log("Processing multi key item:", item)

      const processedItem = {
        message: item.message || "",
        caption: item.caption || "",
        status: item.status || "0",
        link: item.link || "",
        isLocal: false,
        fileData: null
      }

      // Check if this is a cloud file (starts with http/https)
      if (item.link && item.link.startsWith('http')) {
        console.log(`Cloud file detected: ${item.link}`)
        processedItem.isLocal = false
      } else if (item.link && !item.link.startsWith('http')) {
        console.log(`Local file detected: ${item.link}`)
        processedItem.isLocal = true
        processedItem.localFileName = item.link
      } else {
        console.log(`No link provided for item:`, item)
      }

      processedItems.push(processedItem)
    }

    console.log(`Processed ${processedItems.length} multi key items`)
    sendResponse({ success: true, data: processedItems })

  } catch (error) {
    console.error("Multi key processing error:", error)
    sendResponse({ success: false, error: error.message })
  }
}

// Download file from URL
async function handleDownloadFile(request, sendResponse) {
  try {
    const { url, fileName } = request

    if (!url) {
      throw new Error("File URL is required")
    }

    console.log("Downloading file from URL:", url)

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "*/*",
        "Cache-Control": "no-cache",
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const blob = await response.blob()
    const arrayBuffer = await blob.arrayBuffer()
    const uint8Array = new Uint8Array(arrayBuffer)

    // Determine file name and type
    const finalFileName = fileName || url.split('/').pop()?.split('?')[0] || 'downloaded_file'
    const fileType = blob.type || 'application/octet-stream'

    const fileData = {
      name: finalFileName,
      type: fileType,
      size: uint8Array.length,
      data: Array.from(uint8Array), // Convert to regular array for JSON serialization
      lastModified: Date.now()
    }

    console.log(`File downloaded successfully: ${finalFileName} (${fileData.size} bytes)`)
    sendResponse({ success: true, data: fileData })

  } catch (error) {
    console.error("File download error:", error)
    sendResponse({ success: false, error: error.message })
  }
}

// Read local file (Note: This is limited by browser security - actual implementation would need file system access)
async function handleReadLocalFile(request, sendResponse) {
  try {
    const { filePath } = request

    // Browser extensions have limited file system access
    // This is a placeholder - in a real implementation, you'd need:
    // 1. Native messaging host
    // 2. File system permissions
    // 3. Or user file selection

    console.log("Local file reading requested for:", filePath)

    // For now, return an error indicating this needs to be implemented differently
    throw new Error("Local file reading requires additional setup. Please use the file upload feature instead.")

  } catch (error) {
    console.error("Local file reading error:", error)
    sendResponse({ success: false, error: error.message })
  }
}

// Note: Local file reading is now handled in the popup script via folder selection
// This eliminates the need for a separate local file reading function
