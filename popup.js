document.addEventListener("DOMContentLoaded", () => {
  // Tab functionality
  const tabs = document.querySelectorAll(".tab")
  const tabContents = document.querySelectorAll(".tab-content")
  const chrome = window.chrome // Declare the chrome variable

  // File upload elements
  const fileInput = document.getElementById("fileInput")
  const fileUploadButton = document.getElementById("fileUploadButton")
  const fileUploadContainer = document.getElementById("fileUploadContainer")
  const filePreview = document.getElementById("filePreview")
  const fileList = document.getElementById("fileList")
  const clearAllFiles = document.getElementById("clearAllFiles")
  const fileTypeButtons = document.querySelectorAll(".file-type-btn")

  // File management - store as simple objects instead of File objects
  let selectedFiles = []
  let currentFileType = "all"

  tabs.forEach((tab) => {
    tab.addEventListener("click", () => {
      const targetTab = tab.getAttribute("data-tab")

      // Remove active class from all tabs and contents
      tabs.forEach((t) => t.classList.remove("active"))
      tabContents.forEach((tc) => tc.classList.remove("active"))

      // Add active class to clicked tab and corresponding content
      tab.classList.add("active")
      document.getElementById(`${targetTab}-tab`).classList.add("active")

      // Save active tab to storage
      saveToStorage({ activeTab: targetTab })
    })
  })

  // Manual Tab Elements
  const triggerButton = document.getElementById("trigger")
  const statusDiv = document.getElementById("status")
  const phoneInput = document.getElementById("phoneNumber")
  const messageInput = document.getElementById("message")

  // API Tab Elements
  const fetchQueueButton = document.getElementById("fetchQueue")
  const startBulkButton = document.getElementById("startBulk")
  const stopBulkButton = document.getElementById("stopBulk")
  const apiUrlInput = document.getElementById("apiUrl")
  const userIdInput = document.getElementById("userId")
  const secretInput = document.getElementById("secret")
  const localFolderPathInput = document.getElementById("localFolderPath")
  const localFilesInput = document.getElementById("localFilesInput")
  const localFilesPreview = document.getElementById("localFilesPreview")
  const localFilesList = document.getElementById("localFilesList")
  const clearLocalFilesButton = document.getElementById("clearLocalFiles")
  const delaySelect = document.getElementById("delay")
  const queueContainer = document.getElementById("queueContainer")
  const queueList = document.getElementById("queueList")
  const progressContainer = document.getElementById("progress-container")
  const progressBar = document.getElementById("progressBar")
  const progressText = document.getElementById("progressText")

  // Add direct test button and debug area
  const apiTabContent = document.getElementById("api-tab")

  // Create debug area
  const debugArea = document.createElement("div")
  debugArea.style.margin = "10px 0"
  debugArea.style.padding = "10px"
  debugArea.style.backgroundColor = "#f8f9fa"
  debugArea.style.border = "1px solid #ddd"
  debugArea.style.borderRadius = "5px"
  debugArea.id = "debugArea"

  const debugTitle = document.createElement("h4")
  debugTitle.textContent = "Debug Information:"
  debugTitle.style.margin = "0 0 10px 0"

  const debugContent = document.createElement("pre")
  debugContent.style.margin = "0"
  debugContent.style.fontSize = "12px"
  debugContent.style.maxHeight = "200px"
  debugContent.style.overflow = "auto"
  debugContent.style.whiteSpace = "pre-wrap"
  debugContent.style.wordBreak = "break-all"
  debugContent.id = "debugContent"

  debugArea.appendChild(debugTitle)
  debugArea.appendChild(debugContent)

  // Create test buttons container
  const testButtonsContainer = document.createElement("div")
  testButtonsContainer.style.display = "flex"
  testButtonsContainer.style.gap = "10px"
  testButtonsContainer.style.marginTop = "10px"

  const directTestButton = document.createElement("button")
  directTestButton.textContent = "Test API Connection"
  directTestButton.className = "secondary-btn"
  directTestButton.style.flex = "1"
  directTestButton.style.margin = "0"

  const browserTestButton = document.createElement("button")
  browserTestButton.textContent = "Open API in Browser"
  browserTestButton.className = "secondary-btn"
  browserTestButton.style.flex = "1"
  browserTestButton.style.margin = "0"
  browserTestButton.style.backgroundColor = "#17a2b8"

  testButtonsContainer.appendChild(directTestButton)
  testButtonsContainer.appendChild(browserTestButton)

  apiTabContent.insertBefore(testButtonsContainer, fetchQueueButton.nextSibling)
  apiTabContent.insertBefore(debugArea, queueContainer)

  // File Upload Event Listeners
  setupFileUpload()

  // API Bulk File Upload Elements
  const apiBulkFileInput = document.getElementById("apiBulkFileInput")
  const apiBulkFileUploadButton = document.getElementById("apiBulkFileUploadButton")
  const apiBulkFileUploadContainer = document.getElementById("apiBulkFileUploadContainer")
  const apiBulkFilePreview = document.getElementById("apiBulkFilePreview")
  const apiBulkFileList = document.getElementById("apiBulkFileList")
  const apiBulkClearAllFiles = document.getElementById("apiBulkClearAllFiles")
  const apiBulkFileTypeButtons = document.querySelectorAll("#api-tab .file-type-btn")

  // API Bulk file management
  let apiBulkSelectedFiles = []
  let apiBulkCurrentFileType = "all"

  // Setup API bulk file upload
  function setupApiBulkFileUpload() {
    // File type filter buttons
    apiBulkFileTypeButtons.forEach((button) => {
      button.addEventListener("click", () => {
        apiBulkFileTypeButtons.forEach((btn) => btn.classList.remove("active"))
        button.classList.add("active")
        apiBulkCurrentFileType = button.dataset.type
        updateApiBulkFileInputAccept()
      })
    })

    // File upload button click
    apiBulkFileUploadButton.addEventListener("click", () => {
      apiBulkFileInput.click()
    })

    // File input change
    apiBulkFileInput.addEventListener("change", (e) => {
      handleApiBulkFileSelection(Array.from(e.target.files))
    })

    // Drag and drop
    apiBulkFileUploadContainer.addEventListener("dragover", (e) => {
      e.preventDefault()
      apiBulkFileUploadContainer.classList.add("dragover")
    })

    apiBulkFileUploadContainer.addEventListener("dragleave", (e) => {
      e.preventDefault()
      apiBulkFileUploadContainer.classList.remove("dragover")
    })

    apiBulkFileUploadContainer.addEventListener("drop", (e) => {
      e.preventDefault()
      apiBulkFileUploadContainer.classList.remove("dragover")
      handleApiBulkFileSelection(Array.from(e.dataTransfer.files))
    })

    // Clear all files
    apiBulkClearAllFiles.addEventListener("click", () => {
      apiBulkSelectedFiles = []
      updateApiBulkFilePreview()
      saveApiBulkFilesToStorage()
    })
  }

  function updateApiBulkFileInputAccept() {
    const acceptMap = {
      all: "image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar",
      image: "image/*",
      document: ".pdf,.doc,.docx,.txt,.zip,.rar",
      video: "video/*",
      audio: "audio/*",
    }
    apiBulkFileInput.accept = acceptMap[apiBulkCurrentFileType] || acceptMap.all
  }

  function handleApiBulkFileSelection(files) {
    console.log("=== API BULK FILE SELECTION DEBUG ===")
    console.log("Raw files received:", files)
    console.log("Number of files:", files.length)

    const maxFileSize = 16 * 1024 * 1024 // 16MB
    const validFiles = []

    files.forEach((file, index) => {
      console.log(`Processing API bulk file ${index + 1}:`, file.name, file.size, file.type)

      // Validate file object
      if (!file || !file.name || file.size === undefined || file.size === null) {
        console.error(`Invalid file object at index ${index}:`, file)
        showStatus(`Invalid file object detected at index ${index}`, "error")
        return
      }

      if (file.size > maxFileSize) {
        console.warn(`File too large:`, file.size, "bytes")
        showStatus(`File "${file.name}" is too large. Maximum size is 16MB.`, "error")
        return
      }

      if (file.size === 0) {
        console.warn(`File is empty:`, file.name)
        showStatus(`File "${file.name}" is empty and cannot be uploaded.`, "error")
        return
      }

      if (apiBulkCurrentFileType !== "all" && !isFileTypeAllowed(file, apiBulkCurrentFileType)) {
        console.warn(`File type not allowed:`, file.type, "for filter:", apiBulkCurrentFileType)
        showStatus(`File "${file.name}" is not allowed for selected type.`, "error")
        return
      }

      // Check if file already exists
      const existingFile = apiBulkSelectedFiles.find((f) => f.name === file.name && f.size === file.size)
      if (!existingFile) {
        // Convert File to simple object immediately
        const fileObj = {
          name: file.name,
          type: file.type || "application/octet-stream",
          size: file.size,
          lastModified: file.lastModified || Date.now(),
          file: file, // Keep reference to original File object
        }

        console.log(`API bulk file processed successfully:`, fileObj)
        validFiles.push(fileObj)
      } else {
        console.log(`File already exists, skipping:`, file.name)
      }
    })

    console.log("Valid API bulk files processed:", validFiles.length)

    if (validFiles.length > 0) {
      apiBulkSelectedFiles.push(...validFiles)
      console.log("Total API bulk selected files:", apiBulkSelectedFiles.length)
      updateApiBulkFilePreview()
      saveApiBulkFilesToStorage()
      showStatus(`Added ${validFiles.length} file(s) for bulk sending`, "success")
    } else {
      console.log("No valid files to add for API bulk")
    }
  }

  function updateApiBulkFilePreview() {
    if (apiBulkSelectedFiles.length === 0) {
      apiBulkFilePreview.classList.remove("show")
      return
    }

    apiBulkFilePreview.classList.add("show")
    apiBulkFileList.innerHTML = ""

    apiBulkSelectedFiles.forEach((fileObj, index) => {
      const fileItem = document.createElement("div")
      fileItem.className = "file-item"

      const fileIcon = getFileIcon(fileObj)
      const fileSize = formatFileSize(fileObj.size)

      fileItem.innerHTML = `
      <div class="file-info">
        <div class="file-icon">${fileIcon}</div>
        <div class="file-details">
          <div class="file-name">${fileObj.name}</div>
          <div class="file-size">${fileSize}</div>
        </div>
      </div>
      <button class="file-remove" data-index="${index}">Remove</button>
    `

      apiBulkFileList.appendChild(fileItem)
    })

    // Add remove button listeners
    apiBulkFileList.querySelectorAll(".file-remove").forEach((button) => {
      button.addEventListener("click", (e) => {
        const index = Number.parseInt(e.target.dataset.index)
        apiBulkSelectedFiles.splice(index, 1)
        updateApiBulkFilePreview()
        saveApiBulkFilesToStorage()
      })
    })
  }

  function saveApiBulkFilesToStorage() {
    console.log("Saving API bulk files to storage:", apiBulkSelectedFiles.length)

    if (apiBulkSelectedFiles.length === 0) {
      saveToStorage({ apiBulkSelectedFiles: [] })
      return
    }

    // Convert files to base64 for storage
    const filePromises = apiBulkSelectedFiles.map((fileObj, index) => {
      return new Promise((resolve) => {
        console.log(`Converting API bulk file ${index + 1} for storage:`, fileObj.name)

        // If we don't have the original file object, save what we have
        if (!fileObj.file) {
          console.log(`API bulk file ${index + 1}: No original file object, saving metadata only`)
          const savedData = {
            name: fileObj.name,
            type: fileObj.type,
            size: fileObj.size,
            lastModified: fileObj.lastModified,
            data: fileObj.data || null,
          }
          resolve(savedData)
          return
        }

        // Convert file to base64
        console.log(`API bulk file ${index + 1}: Converting to base64...`)
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const result = {
              name: fileObj.name,
              type: fileObj.type,
              size: fileObj.size,
              lastModified: fileObj.lastModified,
              data: e.target.result,
            }
            console.log(`API bulk file ${index + 1} converted successfully`)
            resolve(result)
          } catch (error) {
            console.error(`Error processing API bulk file ${index + 1}:`, error)
            resolve({
              name: fileObj.name,
              type: fileObj.type,
              size: fileObj.size,
              lastModified: fileObj.lastModified,
              data: null,
            })
          }
        }
        reader.onerror = (error) => {
          console.error(`FileReader error for API bulk file ${index + 1}:`, error)
          resolve({
            name: fileObj.name,
            type: fileObj.type,
            size: fileObj.size,
            lastModified: fileObj.lastModified,
            data: null,
          })
        }
        reader.readAsDataURL(fileObj.file)
      })
    })

    Promise.all(filePromises)
      .then((fileDataArray) => {
        console.log("API bulk files processed for storage:", fileDataArray.length)
        const validFileData = fileDataArray.filter((data) => data.data !== null)
        console.log("Valid API bulk files for storage:", validFileData.length)
        saveToStorage({ apiBulkSelectedFiles: validFileData })
      })
      .catch((error) => {
        console.error("Error saving API bulk files to storage:", error)
      })
  }

  async function loadApiBulkFilesFromStorage() {
    console.log("Loading API bulk files from storage...")

    const savedData = await loadFromStorage(["apiBulkSelectedFiles"])
    console.log("Raw saved API bulk data:", savedData)

    if (savedData.apiBulkSelectedFiles && Array.isArray(savedData.apiBulkSelectedFiles)) {
      console.log("Found saved API bulk files:", savedData.apiBulkSelectedFiles.length)

      try {
        apiBulkSelectedFiles = []

        for (let index = 0; index < savedData.apiBulkSelectedFiles.length; index++) {
          const fileData = savedData.apiBulkSelectedFiles[index]
          console.log(`Loading API bulk file ${index + 1}:`, fileData.name)

          try {
            // Validate file data structure
            if (!fileData.name || !fileData.type || fileData.size === undefined) {
              console.warn(`Invalid API bulk file data structure for file ${index + 1}:`, fileData)
              continue
            }

            // Create file object structure
            const fileObj = {
              name: fileData.name,
              type: fileData.type,
              size: fileData.size,
              lastModified: fileData.lastModified || Date.now(),
              data: fileData.data,
              file: null,
            }

            console.log(`API bulk file ${index + 1} loaded successfully:`, fileObj.name)
            apiBulkSelectedFiles.push(fileObj)
          } catch (fileError) {
            console.error(`Error processing API bulk file ${index + 1}:`, fileError)
          }
        }

        console.log("Successfully loaded API bulk files:", apiBulkSelectedFiles.length)
        updateApiBulkFilePreview()
      } catch (error) {
        console.error("Error loading API bulk files from storage:", error)
        apiBulkSelectedFiles = []
      }
    } else {
      console.log("No saved API bulk files found")
    }
  }

  // Global variables for bulk sending
  let messageQueue = []
  let isProcessing = false
  let currentIndex = 0
  let preSelectedLocalFiles = new Map() // Map of filename -> File object for local file matching

  function setupFileUpload() {
    // File type filter buttons
    fileTypeButtons.forEach((button) => {
      button.addEventListener("click", () => {
        fileTypeButtons.forEach((btn) => btn.classList.remove("active"))
        button.classList.add("active")
        currentFileType = button.dataset.type
        updateFileInputAccept()
      })
    })

    // File upload button click
    fileUploadButton.addEventListener("click", () => {
      fileInput.click()
    })

    // File input change
    fileInput.addEventListener("change", (e) => {
      handleFileSelection(Array.from(e.target.files))
    })

    // Drag and drop
    fileUploadContainer.addEventListener("dragover", (e) => {
      e.preventDefault()
      fileUploadContainer.classList.add("dragover")
    })

    fileUploadContainer.addEventListener("dragleave", (e) => {
      e.preventDefault()
      fileUploadContainer.classList.remove("dragover")
    })

    fileUploadContainer.addEventListener("drop", (e) => {
      e.preventDefault()
      fileUploadContainer.classList.remove("dragover")
      handleFileSelection(Array.from(e.dataTransfer.files))
    })

    // Clear all files
    clearAllFiles.addEventListener("click", () => {
      selectedFiles = []
      updateFilePreview()
      saveFilesToStorage()
    })
  }

  function updateFileInputAccept() {
    const acceptMap = {
      all: "image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar",
      image: "image/*",
      document: ".pdf,.doc,.docx,.txt,.zip,.rar",
      video: "video/*",
      audio: "audio/*",
    }
    fileInput.accept = acceptMap[currentFileType] || acceptMap.all
  }

  function handleFileSelection(files) {
    console.log("=== FILE SELECTION DEBUG ===")
    console.log("Raw files received:", files)
    console.log("Number of files:", files.length)

    const maxFileSize = 16 * 1024 * 1024 // 16MB
    const validFiles = []

    files.forEach((file, index) => {
      console.log(`\n--- Processing file ${index + 1} ---`)
      console.log("File object:", file)
      console.log("File name:", file?.name)
      console.log("File size:", file?.size)
      console.log("File type:", file?.type)
      console.log("File lastModified:", file?.lastModified)
      console.log("File constructor:", file?.constructor?.name)

      // Validate file object
      if (!file || !file.name || file.size === undefined || file.size === null) {
        console.error(`❌ File ${index + 1} validation failed:`, {
          hasFile: !!file,
          hasName: !!file?.name,
          hasSize: file?.size !== undefined && file?.size !== null,
          file: file,
        })
        showStatus(`Invalid file object detected at index ${index}`, "error")
        return
      }

      if (file.size > maxFileSize) {
        console.warn(`❌ File ${index + 1} too large:`, file.size, "bytes")
        showStatus(`File "${file.name}" is too large. Maximum size is 16MB.`, "error")
        return
      }

      if (file.size === 0) {
        console.warn(`❌ File ${index + 1} is empty`)
        showStatus(`File "${file.name}" is empty and cannot be uploaded.`, "error")
        return
      }

      if (currentFileType !== "all" && !isFileTypeAllowed(file, currentFileType)) {
        console.warn(`❌ File ${index + 1} type not allowed:`, file.type, "for filter:", currentFileType)
        showStatus(`File "${file.name}" is not allowed for selected type.`, "error")
        return
      }

      // Check if file already exists
      const existingFile = selectedFiles.find((f) => f.name === file.name && f.size === file.size)
      if (!existingFile) {
        // Convert File to simple object immediately
        const fileObj = {
          name: file.name,
          type: file.type || "application/octet-stream",
          size: file.size,
          lastModified: file.lastModified || Date.now(),
          file: file, // Keep reference to original File object
        }

        console.log(`✅ File ${index + 1} processed successfully:`, fileObj)
        validFiles.push(fileObj)
      } else {
        console.log(`⚠️ File ${index + 1} already exists, skipping`)
      }
    })

    console.log("\n=== FILE SELECTION SUMMARY ===")
    console.log("Valid files processed:", validFiles.length)
    console.log("Valid files:", validFiles)

    if (validFiles.length > 0) {
      selectedFiles.push(...validFiles)
      console.log("Total selected files after addition:", selectedFiles.length)
      updateFilePreview()
      saveFilesToStorage()
      showStatus(`Added ${validFiles.length} file(s)`, "success")
    } else {
      console.log("No valid files to add")
    }
  }

  function isFileTypeAllowed(file, type) {
    const typeMap = {
      image: file.type.startsWith("image/"),
      video: file.type.startsWith("video/"),
      audio: file.type.startsWith("audio/"),
      document:
        file.type === "application/pdf" ||
        file.type.includes("document") ||
        file.type === "text/plain" ||
        file.type.includes("zip") ||
        file.type.includes("rar"),
    }
    return typeMap[type] || false
  }

  function updateFilePreview() {
    if (selectedFiles.length === 0) {
      filePreview.classList.remove("show")
      return
    }

    filePreview.classList.add("show")
    fileList.innerHTML = ""

    selectedFiles.forEach((fileObj, index) => {
      const fileItem = document.createElement("div")
      fileItem.className = "file-item"

      const fileIcon = getFileIcon(fileObj)
      const fileSize = formatFileSize(fileObj.size)

      fileItem.innerHTML = `
        <div class="file-info">
          <div class="file-icon">${fileIcon}</div>
          <div class="file-details">
            <div class="file-name">${fileObj.name}</div>
            <div class="file-size">${fileSize}</div>
          </div>
        </div>
        <button class="file-remove" data-index="${index}">Remove</button>
      `

      fileList.appendChild(fileItem)
    })

    // Add remove button listeners
    fileList.querySelectorAll(".file-remove").forEach((button) => {
      button.addEventListener("click", (e) => {
        const index = Number.parseInt(e.target.dataset.index)
        selectedFiles.splice(index, 1)
        updateFilePreview()
        saveFilesToStorage()
      })
    })
  }

  function getFileIcon(fileObj) {
    if (fileObj.type.startsWith("image/")) return "🖼️"
    if (fileObj.type.startsWith("video/")) return "🎥"
    if (fileObj.type.startsWith("audio/")) return "🎵"
    if (fileObj.type === "application/pdf") return "📄"
    if (fileObj.type.includes("document")) return "📝"
    if (fileObj.type.includes("zip") || fileObj.type.includes("rar")) return "📦"
    return "📎"
  }

  function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  function saveFilesToStorage() {
    console.log("\n=== SAVE FILES TO STORAGE DEBUG ===")
    console.log("Selected files to save:", selectedFiles.length)

    if (selectedFiles.length === 0) {
      console.log("No files to save, clearing storage")
      saveToStorage({ selectedFiles: [] })
      return
    }

    // Convert files to base64 for storage (only if they have the original file object)
    const filePromises = selectedFiles.map((fileObj, index) => {
      return new Promise((resolve) => {
        console.log(`\n--- Converting file ${index + 1} for storage ---`)
        console.log("File object:", fileObj)
        console.log("Has original file:", !!fileObj.file)
        console.log("Has existing data:", !!fileObj.data)

        // If we don't have the original file object, save what we have
        if (!fileObj.file) {
          console.log(`File ${index + 1}: No original file object, saving metadata only`)
          const savedData = {
            name: fileObj.name,
            type: fileObj.type,
            size: fileObj.size,
            lastModified: fileObj.lastModified,
            data: fileObj.data || null, // May already have base64 data
          }
          console.log(`File ${index + 1} saved data:`, {
            ...savedData,
            data: savedData.data ? `[${savedData.data.length} chars]` : null,
          })
          resolve(savedData)
          return
        }

        // Convert file to base64
        console.log(`File ${index + 1}: Converting to base64...`)
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const result = {
              name: fileObj.name,
              type: fileObj.type,
              size: fileObj.size,
              lastModified: fileObj.lastModified,
              data: e.target.result,
            }
            console.log(`✅ File ${index + 1} converted successfully:`, {
              ...result,
              data: result.data ? `[${result.data.length} chars]` : null,
            })
            resolve(result)
          } catch (error) {
            console.error(`❌ Error processing file ${index + 1} for storage:`, fileObj.name, error)
            resolve({
              name: fileObj.name,
              type: fileObj.type,
              size: fileObj.size,
              lastModified: fileObj.lastModified,
              data: null,
            })
          }
        }
        reader.onerror = (error) => {
          console.error(`❌ FileReader error for file ${index + 1}:`, fileObj.name, error)
          resolve({
            name: fileObj.name,
            type: fileObj.type,
            size: fileObj.size,
            lastModified: fileObj.lastModified,
            data: null,
          })
        }
        reader.readAsDataURL(fileObj.file)
      })
    })

    Promise.all(filePromises)
      .then((fileDataArray) => {
        console.log("\n=== STORAGE CONVERSION RESULTS ===")
        console.log("Total files processed:", fileDataArray.length)

        // Filter out files without data
        const validFileData = fileDataArray.filter((data, index) => {
          const isValid = data.data !== null
          console.log(`File ${index + 1} valid for storage:`, isValid)
          if (!isValid) {
            console.warn(`File ${index + 1} has no data, excluding from storage:`, data.name)
          }
          return isValid
        })

        console.log("Valid files for storage:", validFileData.length)
        saveToStorage({ selectedFiles: validFileData })
        console.log(`✅ Saved ${validFileData.length} files to storage`)
      })
      .catch((error) => {
        console.error("❌ Error saving files to storage:", error)
      })
  }

  async function loadFilesFromStorage() {
    console.log("\n=== LOAD FILES FROM STORAGE DEBUG ===")

    const savedData = await loadFromStorage(["selectedFiles"])
    console.log("Raw saved data:", savedData)

    if (savedData.selectedFiles && Array.isArray(savedData.selectedFiles)) {
      console.log("Found saved files:", savedData.selectedFiles.length)

      try {
        selectedFiles = []

        for (let index = 0; index < savedData.selectedFiles.length; index++) {
          const fileData = savedData.selectedFiles[index]
          console.log(`\n--- Loading file ${index + 1} ---`)
          console.log("File data:", { ...fileData, data: fileData.data ? `[${fileData.data.length} chars]` : null })

          try {
            // Validate file data structure
            if (!fileData.name || !fileData.type || fileData.size === undefined) {
              console.warn(`❌ Invalid file data structure for file ${index + 1}:`, fileData)
              continue
            }

            // Create file object structure (without converting back to File object yet)
            const fileObj = {
              name: fileData.name,
              type: fileData.type,
              size: fileData.size,
              lastModified: fileData.lastModified || Date.now(),
              data: fileData.data, // Keep base64 data
              file: null, // No original File object
            }

            console.log(`✅ File ${index + 1} loaded successfully:`, {
              ...fileObj,
              data: fileObj.data ? `[${fileObj.data.length} chars]` : null,
            })
            selectedFiles.push(fileObj)
          } catch (fileError) {
            console.error(`❌ Error processing file ${index + 1}:`, fileData.name, fileError)
          }
        }

        console.log("\n=== LOAD FILES SUMMARY ===")
        console.log("Successfully loaded files:", selectedFiles.length)
        updateFilePreview()
        console.log(`✅ Loaded ${selectedFiles.length} files from storage`)
      } catch (error) {
        console.error("❌ Error loading files from storage:", error)
        selectedFiles = []
      }
    } else {
      console.log("No saved files found or invalid data structure")
    }
  }

  // Convert file objects to File objects when needed for sending
  function convertToFileObjects(fileObjects) {
    console.log("\n=== CONVERT TO FILE OBJECTS DEBUG ===")
    console.log("Input file objects:", fileObjects.length)

    const results = fileObjects
      .map((fileObj, index) => {
        console.log(`\n--- Converting file object ${index + 1} ---`)
        console.log("File object:", { ...fileObj, data: fileObj.data ? `[${fileObj.data.length} chars]` : null })

        try {
          // If we already have a File object, use it
          if (fileObj.file && fileObj.file instanceof File) {
            console.log(`✅ File ${index + 1}: Using existing File object`)
            return fileObj.file
          }

          // Handle local files with no data (placeholder files)
          if (fileObj.isLocalFile && (!fileObj.data || fileObj.data.length === 0)) {
            console.log(`File ${index + 1}: Local file placeholder - creating minimal File object`)
            // Create a minimal File object for local files that will be handled by content script
            const file = new File([''], fileObj.name, {
              type: fileObj.type || 'application/octet-stream',
              lastModified: fileObj.lastModified || Date.now(),
            })

            // Add the local file metadata
            file.isLocalFile = fileObj.isLocalFile
            file.localFileName = fileObj.localFileName
            file.individualCaption = fileObj.individualCaption
            file.individualMessage = fileObj.individualMessage
            file.isMultiKeyFile = fileObj.isMultiKeyFile

            console.log(`✅ File ${index + 1}: Local file placeholder created`)
            return file
          }

          // If we have data, convert it back to File
          if (fileObj.data && fileObj.data.length > 0) {
            console.log(`File ${index + 1}: Converting file data...`)

            let byteArray

            // Check if data is already an array of numbers (from download) or base64 string
            if (Array.isArray(fileObj.data)) {
              console.log(`File ${index + 1}: Data is array format (${fileObj.data.length} bytes)`)
              byteArray = new Uint8Array(fileObj.data)
            } else if (typeof fileObj.data === 'string') {
              console.log(`File ${index + 1}: Data is base64 format`)
              const base64Data = fileObj.data.split(",")[1]
              if (!base64Data) {
                console.warn(`❌ File ${index + 1}: Invalid base64 data format`)
                return null
              }

              console.log(`File ${index + 1}: Base64 data length:`, base64Data.length)

              const byteCharacters = atob(base64Data)
              const byteNumbers = new Array(byteCharacters.length)
              for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i)
              }
              byteArray = new Uint8Array(byteNumbers)
            } else {
              console.warn(`❌ File ${index + 1}: Unknown data format`)
              return null
            }

            const file = new File([byteArray], fileObj.name, {
              type: fileObj.type,
              lastModified: fileObj.lastModified,
            })

            // Copy metadata for multi key files
            if (fileObj.isMultiKeyFile) {
              file.isMultiKeyFile = fileObj.isMultiKeyFile
              file.individualCaption = fileObj.individualCaption
              file.individualMessage = fileObj.individualMessage
            }
            if (fileObj.isLocalFile) {
              file.isLocalFile = fileObj.isLocalFile
              file.localFileName = fileObj.localFileName
            }

            console.log(`✅ File ${index + 1}: Successfully converted to File object:`, {
              name: file.name,
              size: file.size,
              type: file.type,
              lastModified: file.lastModified,
              isMultiKeyFile: file.isMultiKeyFile || false,
              isLocalFile: file.isLocalFile || false,
            })

            return file
          }

          // If no data available, return null
          console.warn(`❌ File ${index + 1}: No file data available`)
          return null
        } catch (error) {
          console.error(`❌ Error converting file object ${index + 1}:`, fileObj.name, error)
          return null
        }
      })
      .filter((file, index) => {
        const isValid = file !== null
        if (!isValid) {
          console.warn(`File ${index + 1} filtered out (null result)`)
        }
        return isValid
      })

    console.log("\n=== CONVERSION SUMMARY ===")
    console.log("Input objects:", fileObjects.length)
    console.log("Successfully converted:", results.length)
    console.log("Conversion success rate:", `${Math.round((results.length / fileObjects.length) * 100)}%`)

    return results
  }

  // Convert File objects to serializable format for message passing
  async function serializeFilesForMessage(files) {
    console.log("\n=== SERIALIZE FILES FOR MESSAGE PASSING ===")
    console.log("Files to serialize:", files.length)

    const serializedFiles = []

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      console.log(`\n--- Serializing file ${i + 1} ---`)
      console.log("File:", {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
        isFile: file instanceof File,
      })

      try {
        // Convert File to ArrayBuffer for message passing
        const arrayBuffer = await file.arrayBuffer()
        const uint8Array = new Uint8Array(arrayBuffer)

        const serializedFile = {
          name: file.name,
          type: file.type,
          size: file.size,
          lastModified: file.lastModified,
          data: Array.from(uint8Array), // Convert to regular array for JSON serialization
        }

        // Preserve multi key file metadata
        if (file.isMultiKeyFile) {
          serializedFile.isMultiKeyFile = file.isMultiKeyFile
          serializedFile.individualCaption = file.individualCaption
          serializedFile.individualMessage = file.individualMessage
        }
        if (file.isLocalFile) {
          serializedFile.isLocalFile = file.isLocalFile
          serializedFile.localFileName = file.localFileName
        }

        console.log(`✅ File ${i + 1} serialized successfully:`, {
          name: serializedFile.name,
          size: serializedFile.size,
          type: serializedFile.type,
          dataLength: serializedFile.data.length,
        })

        serializedFiles.push(serializedFile)
      } catch (error) {
        console.error(`❌ Error serializing file ${i + 1}:`, error)
      }
    }

    console.log("\n=== SERIALIZATION SUMMARY ===")
    console.log("Input files:", files.length)
    console.log("Successfully serialized:", serializedFiles.length)

    return serializedFiles
  }

  // Storage functions
  function saveToStorage(data) {
    try {
      chrome.storage.local.set(data, () => {
        if (chrome.runtime.lastError) {
          console.error("Error saving to storage:", chrome.runtime.lastError)
        }
      })
    } catch (error) {
      console.error("Storage save error:", error)
    }
  }

  function loadFromStorage(keys) {
    return new Promise((resolve) => {
      try {
        chrome.storage.local.get(keys, (result) => {
          if (chrome.runtime.lastError) {
            console.error("Error loading from storage:", chrome.runtime.lastError)
            resolve({})
          } else {
            resolve(result)
          }
        })
      } catch (error) {
        console.error("Storage load error:", error)
        resolve({})
      }
    })
  }

  // Handle local files selection
  async function handleLocalFilesSelection(event) {
    const files = Array.from(event.target.files)
    console.log(`\n=== LOCAL FILES SELECTION ===`)
    console.log(`Selected ${files.length} local files`)

    // Clear previous selections
    preSelectedLocalFiles.clear()

    // Process each selected file
    for (const file of files) {
      console.log(`Processing local file: ${file.name} (${file.size} bytes)`)
      preSelectedLocalFiles.set(file.name, file)
    }

    // Update UI
    updateLocalFilesPreview()

    // Save to storage
    await saveLocalFilesToStorage()

    console.log(`✅ ${preSelectedLocalFiles.size} local files ready for API matching`)
  }

  // Clear all selected local files
  function clearLocalFiles() {
    console.log("Clearing all selected local files")
    preSelectedLocalFiles.clear()
    localFilesInput.value = ""
    updateLocalFilesPreview()
    saveLocalFilesToStorage()
  }

  // Update the local files preview UI
  function updateLocalFilesPreview() {
    if (preSelectedLocalFiles.size === 0) {
      localFilesPreview.style.display = "none"
      return
    }

    localFilesPreview.style.display = "block"
    localFilesList.innerHTML = ""

    preSelectedLocalFiles.forEach((file, filename) => {
      const fileItem = document.createElement("div")
      fileItem.style.cssText = "margin: 3px 0; padding: 5px; background: white; border-radius: 3px; font-size: 12px;"
      fileItem.innerHTML = `
        <span style="font-weight: bold;">${filename}</span>
        <span style="color: #666; margin-left: 10px;">(${formatFileSize(file.size)})</span>
      `
      localFilesList.appendChild(fileItem)
    })
  }

  // Save local files to storage (metadata only, not the actual files)
  async function saveLocalFilesToStorage() {
    const fileMetadata = Array.from(preSelectedLocalFiles.entries()).map(([name, file]) => ({
      name: name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    }))

    await saveToStorage({ localFilesMetadata: fileMetadata })
  }

  // Auto-save form data
  function setupAutoSave() {
    // Save API credentials when they change
    apiUrlInput.addEventListener("input", () => {
      saveToStorage({ apiUrl: apiUrlInput.value })
    })

    userIdInput.addEventListener("input", () => {
      saveToStorage({ userId: userIdInput.value })
    })

    secretInput.addEventListener("input", () => {
      saveToStorage({ secret: secretInput.value })
    })

    localFolderPathInput.addEventListener("input", () => {
      saveToStorage({ localFolderPath: localFolderPathInput.value })
    })

    // Local files selection handling
    localFilesInput.addEventListener("change", handleLocalFilesSelection)
    clearLocalFilesButton.addEventListener("click", clearLocalFiles)

    delaySelect.addEventListener("change", () => {
      saveToStorage({ delay: delaySelect.value })
    })

    // Save manual form data
    phoneInput.addEventListener("input", () => {
      saveToStorage({ phoneNumber: phoneInput.value })
    })

    messageInput.addEventListener("input", () => {
      saveToStorage({ message: messageInput.value })
    })
  }

  // Load saved data on startup
  async function loadSavedData() {
    try {
      const savedData = await loadFromStorage([
        "activeTab",
        "apiUrl",
        "userId",
        "secret",
        "localFolderPath",
        "localFilesMetadata",
        "delay",
        "phoneNumber",
        "message",
        "messageQueue",
        "isProcessing",
        "currentIndex",
        "selectedFiles",
        "apiBulkSelectedFiles",
      ])

      console.log("Loading saved data:", savedData)

      // Restore active tab
      if (savedData.activeTab) {
        const targetTab = savedData.activeTab
        tabs.forEach((t) => t.classList.remove("active"))
        tabContents.forEach((tc) => tc.classList.remove("active"))

        const activeTabButton = document.querySelector(`[data-tab="${targetTab}"]`)
        const activeTabContent = document.getElementById(`${targetTab}-tab`)

        if (activeTabButton && activeTabContent) {
          activeTabButton.classList.add("active")
          activeTabContent.classList.add("active")
        }
      }

      // Restore API credentials
      if (savedData.apiUrl) apiUrlInput.value = savedData.apiUrl
      if (savedData.userId) userIdInput.value = savedData.userId
      if (savedData.secret) secretInput.value = savedData.secret
      if (savedData.localFolderPath) localFolderPathInput.value = savedData.localFolderPath
      if (savedData.delay) delaySelect.value = savedData.delay

      // Restore local files metadata (note: actual files cannot be restored due to browser security)
      if (savedData.localFilesMetadata && Array.isArray(savedData.localFilesMetadata)) {
        console.log(`Found ${savedData.localFilesMetadata.length} local files metadata from previous session`)
        console.log("Note: Actual files need to be re-selected due to browser security restrictions")
        // We can't restore the actual File objects, but we can show the user what was previously selected
        updateLocalFilesPreview() // This will show empty since we can't restore actual files
      }

      // Restore manual form data
      if (savedData.phoneNumber) phoneInput.value = savedData.phoneNumber
      if (savedData.message) messageInput.value = savedData.message

      // Restore message queue
      if (savedData.messageQueue && Array.isArray(savedData.messageQueue)) {
        messageQueue = savedData.messageQueue
        displayQueue(messageQueue)
        startBulkButton.disabled = messageQueue.length === 0
        showStatus(`Restored ${messageQueue.length} messages from previous session`, "info")
      }

      // Restore processing state
      if (savedData.isProcessing) {
        isProcessing = savedData.isProcessing
        currentIndex = savedData.currentIndex || 0

        if (isProcessing) {
          startBulkButton.style.display = "none"
          stopBulkButton.style.display = "block"
          progressContainer.style.display = "block"
          fetchQueueButton.disabled = true
          showStatus("Resumed bulk sending from previous session", "info")
          updateProgress(currentIndex, messageQueue.length)
        }
      }

      // Load saved files
      await loadFilesFromStorage()

      // Load saved API bulk files
      await loadApiBulkFilesFromStorage()
    } catch (error) {
      console.error("Error loading saved data:", error)
    }
  }

  // Save processing state
  function saveProcessingState() {
    saveToStorage({
      messageQueue: messageQueue,
      isProcessing: isProcessing,
      currentIndex: currentIndex,
    })
  }

  function showStatus(message, type) {
    statusDiv.textContent = message
    statusDiv.className = `status ${type}`
    statusDiv.style.display = "block"

    console.log(`Status: ${type} - ${message}`)

    setTimeout(() => {
      statusDiv.style.display = "none"
    }, 8000) // Increased timeout to see errors longer
  }

  function showDebugInfo(info) {
    const debugContent = document.getElementById("debugContent")
    const debugArea = document.getElementById("debugArea")

    debugContent.textContent = typeof info === "string" ? info : JSON.stringify(info, null, 2)
    debugArea.style.display = "block"
  }

  // Check if content script is ready with better error handling
  async function checkContentScriptReady(tabId, maxRetries = 3) {
    console.log(`Checking content script readiness for tab ${tabId}...`)

    for (let i = 0; i < maxRetries; i++) {
      try {
        const response = await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error("Timeout waiting for content script response"))
          }, 3000)

          chrome.tabs.sendMessage(tabId, { action: "ping" }, (response) => {
            clearTimeout(timeout)

            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message))
            } else {
              resolve(response)
            }
          })
        })

        if (response && response.success) {
          console.log("Content script is ready!")
          return true
        }
      } catch (error) {
        console.log(`Content script check attempt ${i + 1} failed:`, error.message)
      }

      // Wait before retrying
      if (i < maxRetries - 1) {
        await new Promise((resolve) => setTimeout(resolve, 2000))
      }
    }

    return false
  }

  // Reload the WhatsApp tab to ensure content script loads
  async function reloadWhatsAppTab(tabId) {
    console.log("Reloading WhatsApp tab to ensure content script loads...")

    try {
      await chrome.tabs.reload(tabId)

      // Wait for reload to complete
      await new Promise((resolve) => setTimeout(resolve, 5000))

      return true
    } catch (error) {
      console.error("Error reloading tab:", error)
      return false
    }
  }

  // Ensure content script is ready with fallback options
  async function ensureContentScript(tabId) {
    try {
      console.log("Ensuring content script is ready...")

      // First attempt: Check if content script is already ready
      let isReady = await checkContentScriptReady(tabId, 2)

      if (!isReady) {
        console.log("Content script not responding, reloading tab...")

        // Reload the tab and try again
        await reloadWhatsAppTab(tabId)

        // Check again after reload
        isReady = await checkContentScriptReady(tabId, 3)

        if (!isReady) {
          throw new Error(
            "Content script failed to load even after tab reload. Please refresh WhatsApp Web manually and try again.",
          )
        }
      }

      return true
    } catch (error) {
      console.error("Error ensuring content script:", error)
      throw error
    }
  }

  function updateProgress(current, total) {
    const percentage = Math.round((current / total) * 100)
    progressBar.style.width = `${percentage}%`
    progressBar.textContent = `${percentage}%`
    progressText.textContent = `Processing ${current} of ${total} messages`

    // Save progress
    saveProcessingState()
  }

  // Validate API credentials
  function validateApiCredentials() {
    const apiUrl = apiUrlInput.value.trim()
    const userId = userIdInput.value.trim()
    const secret = secretInput.value.trim()

    if (!apiUrl || !userId || !secret) {
      showStatus("Please fill in all API credentials", "error")
      return null
    }

    return { apiUrl, userId, secret }
  }

  // Build API URL
  function buildApiUrl(credentials, method = "list_whatsapp_l") {
    let url = credentials.apiUrl
    if (!url.includes("?")) {
      url += "?"
    } else if (!url.endsWith("&") && !url.endsWith("?")) {
      url += "&"
    }

    url += `method=${method}&userid=${encodeURIComponent(credentials.userId)}&secret=${encodeURIComponent(credentials.secret)}`
    return url
  }

  // Direct API fetch function (works around CORS issues)
  async function fetchApiDirect(credentials, method = "list_whatsapp_l", additionalParams = {}) {
    try {
      let url = buildApiUrl(credentials, method)

      // Add additional parameters
      for (const [key, value] of Object.entries(additionalParams)) {
        url += `&${key}=${encodeURIComponent(value)}`
      }

      console.log("Fetching URL:", url)

      // Use different fetch strategies
      const strategies = [
        // Strategy 1: Standard fetch
        () =>
          fetch(url, {
            method: "GET",
            headers: {
              Accept: "*/*",
              "Cache-Control": "no-cache",
            },
            mode: "cors",
          }),

        // Strategy 2: No-cors mode (limited response access)
        () =>
          fetch(url, {
            method: "GET",
            mode: "no-cors",
          }),

        // Strategy 3: Using XMLHttpRequest
        () =>
          new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest()
            xhr.open("GET", url, true)
            xhr.timeout = 30000
            xhr.onload = () => {
              if (xhr.status >= 200 && xhr.status < 300) {
                resolve({
                  ok: true,
                  status: xhr.status,
                  text: () => Promise.resolve(xhr.responseText),
                })
              } else {
                reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText || "Unknown error"}`))
              }
            }
            xhr.onerror = () => reject(new Error("Network error"))
            xhr.ontimeout = () => reject(new Error("Request timeout"))
            xhr.send()
          }),
      ]

      let lastError = null

      for (let i = 0; i < strategies.length; i++) {
        try {
          console.log(`Trying fetch strategy ${i + 1}...`)

          const controller = new AbortController()
          const timeoutId = setTimeout(() => controller.abort(), 30000)

          const response = await strategies[i]()
          clearTimeout(timeoutId)

          console.log("Response status:", response.status)

          if (!response.ok && response.status !== 0) {
            throw new Error(`HTTP ${response.status}: ${response.statusText || "Unknown error"}`)
          }

          const text = await response.text()
          console.log("Response text length:", text.length)
          console.log("Response preview:", text.substring(0, 200))

          return { success: true, text, data: null }
        } catch (error) {
          console.log(`Strategy ${i + 1} failed:`, error.message)
          lastError = error

          if (error.name === "AbortError") {
            throw new Error("Request timed out after 30 seconds")
          }

          // Continue to next strategy
          continue
        }
      }

      throw lastError || new Error("All fetch strategies failed")
    } catch (error) {
      console.error("Direct API fetch error:", error)
      throw error
    }
  }

  // Direct API test function
  async function testApiConnectionDirect() {
    const credentials = validateApiCredentials()
    if (!credentials) return

    directTestButton.disabled = true
    directTestButton.textContent = "Testing..."

    try {
      showStatus("Testing API connection directly...", "info")
      showDebugInfo("Starting direct API test...")

      const result = await fetchApiDirect(credentials)

      if (result.success && result.text) {
        showDebugInfo(
          `Success! Response length: ${result.text.length}\n\nResponse preview:\n${result.text.substring(0, 500)}${result.text.length > 500 ? "...(truncated)" : ""}`,
        )

        try {
          const data = JSON.parse(result.text)
          console.log("Parsed data:", data)

          showStatus("API connection successful! Check debug info below.", "success")

          // Try to parse and display queue
          let queueData = null

          if (Array.isArray(data)) {
            queueData = data
          } else if (data.whatsapp && Array.isArray(data.whatsapp)) {
            queueData = data.whatsapp
          } else if (data.data && Array.isArray(data.data)) {
            queueData = data.data
          } else if (typeof data === "object") {
            // Try to extract array from object
            const possibleArrays = Object.values(data).filter((val) => Array.isArray(val))
            if (possibleArrays.length > 0) {
              queueData = possibleArrays[0]
            }
          }

          if (queueData && queueData.length > 0) {
            messageQueue = queueData
            displayQueue(messageQueue)
            startBulkButton.disabled = false
            showStatus(`Found ${queueData.length} messages in queue!`, "success")
            saveProcessingState() // Save the queue
          } else {
            showStatus("API responded but no queue data found", "error")
            showDebugInfo(`No queue data found in response.\n\nFull response:\n${JSON.stringify(data, null, 2)}`)
          }
        } catch (parseError) {
          console.error("JSON parse error:", parseError)
          showStatus("API responded but returned invalid JSON", "error")
          showDebugInfo(`JSON Parse Error: ${parseError.message}\n\nRaw Response:\n${result.text}`)
        }
      } else {
        throw new Error("No response received")
      }
    } catch (error) {
      console.error("Direct API test error:", error)
      showStatus("API test failed: " + error.message, "error")
      showDebugInfo(
        `Error: ${error.message}\n\nThis could indicate:\n1. Network connectivity issues\n2. CORS restrictions\n3. Invalid API URL or credentials\n4. Server is down`,
      )
    } finally {
      directTestButton.disabled = false
      directTestButton.textContent = "Test API Connection"
    }
  }

  // Open API URL in browser for manual testing
  function openApiInBrowser() {
    const credentials = validateApiCredentials()
    if (!credentials) return

    const url = buildApiUrl(credentials)

    chrome.tabs.create({ url: url }, (tab) => {
      showStatus("Opened API URL in new tab for manual testing", "info")
      showDebugInfo(
        `Opened URL in browser: ${url}\n\nThis will help you see:\n1. If the API is accessible\n2. What the actual response looks like\n3. Any error messages from the server`,
      )
    })
  }

  // Ensure WhatsApp tab is active and ready
  async function ensureWhatsAppTab() {
    console.log("Ensuring WhatsApp tab is ready...")

    const tabs = await chrome.tabs.query({
      url: "https://web.whatsapp.com/*",
    })

    if (tabs.length > 0) {
      // Switch to existing WhatsApp tab
      await chrome.tabs.update(tabs[0].id, { active: true })
      console.log("Switched to existing WhatsApp tab")
      return tabs[0]
    } else {
      // Create new WhatsApp tab
      console.log("Creating new WhatsApp tab...")
      const tab = await chrome.tabs.create({
        url: "https://web.whatsapp.com",
        active: true,
      })

      // Wait for WhatsApp to load
      console.log("Waiting for WhatsApp to load...")
      await new Promise((resolve) => setTimeout(resolve, 8000))

      return tab
    }
  }

  // Improved message sending using content script with longer timeout
  async function sendMessageViaContentScript(tabId, messageData) {
    try {
      const phoneNumber = messageData.mobile || messageData.phone || messageData.number
      console.log(`Sending message to ${phoneNumber}...`, messageData)

      // First navigate to the phone number
      console.log("Navigating to chat...")
      await chrome.tabs.update(tabId, {
        url: `https://web.whatsapp.com/send?phone=${phoneNumber}`,
      })

      // Wait for navigation to complete
      console.log("Waiting for navigation to complete...")
      await new Promise((resolve) => setTimeout(resolve, 8000))

      // Ensure content script is ready
      console.log("Ensuring content script is ready...")
      await ensureContentScript(tabId)

      // Convert file objects to File objects and then serialize for message passing
      if (messageData.files && messageData.files.length > 0) {
        console.log("Converting file objects to File objects...")
        const convertedFiles = convertToFileObjects(messageData.files)
        console.log(`Converted ${convertedFiles.length} files successfully`)

        console.log("Serializing files for message passing...")
        const serializedFiles = await serializeFilesForMessage(convertedFiles)
        messageData.files = serializedFiles
        console.log(`Serialized ${serializedFiles.length} files for content script`)
      }

      // Send message data to content script with increased timeout and progress updates
      console.log("Sending message data to content script...")

      // Increase timeout to 90 seconds for complex messages
      const SEND_TIMEOUT = 120000 // Increased timeout for file uploads

      // Show progress updates to user
      showStatus("Sending message (this may take up to 2 minutes for files)...", "info")

      // Progress indicator
      let progressTimer = null
      let progressSeconds = 0

      progressTimer = setInterval(() => {
        progressSeconds += 5
        showStatus(`Still sending message... (${progressSeconds}s)`, "info")
      }, 5000)

      try {
        const response = await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error("Timeout waiting for message send response"))
          }, SEND_TIMEOUT)

          chrome.tabs.sendMessage(
            tabId,
            {
              action: "sendMessage",
              message: messageData,
              phoneNumber: phoneNumber,
            },
            (response) => {
              clearTimeout(timeout)

              if (chrome.runtime.lastError) {
                console.error("Chrome runtime error:", chrome.runtime.lastError)
                reject(new Error(chrome.runtime.lastError.message))
              } else if (response && response.success) {
                resolve(response)
              } else {
                reject(new Error(response?.error || "Failed to send message"))
              }
            },
          )
        })

        // Clear progress timer
        if (progressTimer) clearInterval(progressTimer)

        console.log("Message sent successfully!")
        return response
      } catch (error) {
        // Clear progress timer on error
        if (progressTimer) clearInterval(progressTimer)
        throw error
      }
    } catch (error) {
      console.error("Error in sendMessageViaContentScript:", error)
      throw error
    }
  }

  // Update the manual message sending to handle the new format
  triggerButton.addEventListener("click", async () => {
    console.log("\n=== MANUAL SEND DEBUG ===")

    const phoneNumber = phoneInput.value.trim()
    const message = messageInput.value.trim()

    console.log("Phone number:", phoneNumber)
    console.log("Message:", message)
    console.log("Selected files count:", selectedFiles.length)
    console.log(
      "Selected files:",
      selectedFiles.map((f) => ({ name: f.name, size: f.size, hasData: !!f.data, hasFile: !!f.file })),
    )

    if (!phoneNumber) {
      showStatus("Please enter a phone number", "error")
      return
    }

    if (!message && selectedFiles.length === 0) {
      showStatus("Please enter a message or select files to send", "error")
      return
    }

    triggerButton.disabled = true
    triggerButton.textContent = "Sending..."

    try {
      showStatus("Preparing WhatsApp tab...", "info")
      const tab = await ensureWhatsAppTab()
      console.log("WhatsApp tab ready:", tab.id)

      // Create message data object with files
      const messageData = {
        mobile: phoneNumber,
        message: message,
        caption: "",
        link: "",
        files: selectedFiles.length > 0 ? selectedFiles : null,
      }

      console.log("Message data prepared:", {
        ...messageData,
        files: messageData.files ? `${messageData.files.length} files` : null,
      })

      showStatus("Sending message with attachments...", "info")
      await sendMessageViaContentScript(tab.id, messageData)

      showStatus("Message sent successfully!", "success")

      // Clear files after successful send
      console.log("Clearing files after successful send")
      selectedFiles = []
      updateFilePreview()
      saveFilesToStorage()
    } catch (error) {
      console.error("❌ Error in manual send:", error)
      showStatus("Error: " + error.message, "error")
    } finally {
      triggerButton.disabled = false
      triggerButton.textContent = "Send Message"
    }
  })

  // Event listeners for test buttons
  directTestButton.addEventListener("click", testApiConnectionDirect)
  browserTestButton.addEventListener("click", openApiInBrowser)

  // API Tab - Fetch Queue (using direct fetch instead of background script)
  fetchQueueButton.addEventListener("click", async () => {
    const credentials = validateApiCredentials()
    if (!credentials) return

    fetchQueueButton.disabled = true
    fetchQueueButton.textContent = "Fetching..."

    try {
      showStatus("Fetching message queue directly...", "info")
      console.log("Fetching queue with credentials:", {
        apiUrl: credentials.apiUrl,
        userId: credentials.userId,
        secretLength: credentials.secret ? credentials.secret.length : 0,
      })

      const result = await fetchApiDirect(credentials)

      if (result.success && result.text) {
        try {
          const data = JSON.parse(result.text)
          console.log("Parsed queue data:", data)

          // Try multiple paths to find the queue data
          let queueData = null

          if (Array.isArray(data)) {
            queueData = data
          } else if (data.whatsapp && Array.isArray(data.whatsapp)) {
            queueData = data.whatsapp
          } else if (data.data && Array.isArray(data.data)) {
            queueData = data.data
          } else if (typeof data === "object") {
            // Try to extract array from object
            const possibleArrays = Object.values(data).filter((val) => Array.isArray(val))
            if (possibleArrays.length > 0) {
              queueData = possibleArrays[0]
            }
          }

          if (!queueData) {
            console.error("Could not find queue data in response:", data)
            throw new Error("Invalid queue data format from API")
          }

          // Process multi key data for each message
          console.log("Processing multi key data for messages...")
          const processedQueue = await processQueueWithMultiKey(queueData)

          messageQueue = processedQueue

          displayQueue(messageQueue)
          startBulkButton.disabled = messageQueue.length === 0
          showStatus(`Found ${messageQueue.length} messages in queue (with multi key processing)`, "success")

          // Save the fetched queue
          saveProcessingState()
        } catch (parseError) {
          console.error("JSON parse error:", parseError)
          showStatus("API responded but returned invalid JSON", "error")
          showDebugInfo(`JSON Parse Error: ${parseError.message}\n\nRaw Response:\n${result.text}`)
        }
      } else {
        throw new Error("No response received from API")
      }
    } catch (error) {
      console.error("Error fetching queue:", error)
      showStatus("Error fetching message queue: " + error.message, "error")
      showDebugInfo(
        `Direct Fetch Error: ${error.message}\n\nTry using the "Test API Connection" button to debug further.`,
      )
    } finally {
      fetchQueueButton.disabled = false
      fetchQueueButton.textContent = "Fetch Message Queue"
    }
  })

  // API Tab - Start Bulk Sending
  startBulkButton.addEventListener("click", async () => {
    if (messageQueue.length === 0) {
      showStatus("No messages in queue", "error")
      return
    }

    try {
      await ensureWhatsAppTab()
    } catch (error) {
      showStatus("Error preparing WhatsApp tab", "error")
      return
    }

    isProcessing = true
    currentIndex = 0
    startBulkButton.style.display = "none"
    stopBulkButton.style.display = "block"
    progressContainer.style.display = "block"
    fetchQueueButton.disabled = true

    // Save processing state
    saveProcessingState()

    processBulkMessages()
  })

  // API Tab - Stop Bulk Sending
  stopBulkButton.addEventListener("click", () => {
    isProcessing = false
    startBulkButton.style.display = "block"
    stopBulkButton.style.display = "none"
    progressContainer.style.display = "none"
    fetchQueueButton.disabled = false
    showStatus("Bulk sending stopped", "info")

    // Save stopped state
    saveProcessingState()
  })

  async function updateMessageStatus(messageId, status) {
    const credentials = validateApiCredentials()
    if (!credentials) return { success: false }

    try {
      const result = await fetchApiDirect(credentials, "update_status", {
        id: messageId,
        status: status,
      })

      if (result.success) {
        try {
          const data = JSON.parse(result.text)
          return { success: true, data }
        } catch (e) {
          return { success: false, error: "Invalid JSON response" }
        }
      } else {
        return { success: false, error: "Request failed" }
      }
    } catch (error) {
      console.error("Error updating message status:", error)
      return { success: false, error: error.message }
    }
  }

  // Process queue with multi key data
  async function processQueueWithMultiKey(queueData) {
    console.log("\n=== POPUP: PROCESSING QUEUE WITH MULTI KEY DATA ===")
    const localFolderPath = localFolderPathInput.value.trim()
    console.log(`Local folder path from input: "${localFolderPath}"`)
    console.log(`Queue data items: ${queueData.length}`)
    const processedQueue = []

    for (const item of queueData) {
      console.log("Processing queue item:", item)

      // Create base processed item
      const processedItem = {
        id: item.id,
        user_id: item.user_id,
        mobile: item.mobile,
        message: item.message || "",
        caption: item.caption || "",
        status: item.status || "0",
        link: item.link || "",
        multi: item.multi || "",
        processedFiles: []
      }

      // Process multi key if it exists
      if (item.multi && item.multi.trim()) {
        try {
          console.log("Processing multi key for item:", item.id)
          const multiData = JSON.parse(item.multi)

          if (multiData.success && Array.isArray(multiData.whatsapp)) {
            console.log(`Found ${multiData.whatsapp.length} items in multi key`)

            for (const multiItem of multiData.whatsapp) {
              const fileItem = {
                message: multiItem.message || "",
                caption: multiItem.caption || "",
                status: multiItem.status || "1",
                link: multiItem.link || "",
                isLocal: false,
                fileData: null,
                error: null
              }

              // Determine if this is a local file or cloud file
              if (multiItem.link && !multiItem.link.startsWith('http')) {
                // Local file
                fileItem.isLocal = true
                fileItem.localFileName = multiItem.link
                console.log(`Local file detected: ${multiItem.link}`)

                // Note: Actual file reading would need to be implemented with file system access
                // For now, we'll mark it as local and handle it during sending

              } else if (multiItem.link && multiItem.link.startsWith('http')) {
                // Cloud file - we'll download it when needed during sending
                fileItem.isLocal = false
                console.log(`Cloud file detected: ${multiItem.link}`)
              }

              processedItem.processedFiles.push(fileItem)
            }
          }
        } catch (multiError) {
          console.error("Error processing multi key for item:", item.id, multiError)
          processedItem.multiError = multiError.message
        }
      }

      processedQueue.push(processedItem)
    }

    console.log(`Processed ${processedQueue.length} queue items with multi key data`)
    return processedQueue
  }

  // Download file from URL
  async function downloadFileFromUrl(url, fileName = null) {
    try {
      console.log("Downloading file from URL:", url)

      const response = await fetch(url, {
        method: "GET",
        headers: {
          Accept: "*/*",
          "Cache-Control": "no-cache",
        },
        mode: "cors"
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const blob = await response.blob()
      const arrayBuffer = await blob.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)

      // Determine file name and type
      const finalFileName = fileName || url.split('/').pop()?.split('?')[0] || 'downloaded_file'
      const fileType = blob.type || 'application/octet-stream'

      const fileData = {
        name: finalFileName,
        type: fileType,
        size: uint8Array.length,
        data: Array.from(uint8Array), // Convert to regular array for JSON serialization
        lastModified: Date.now()
      }

      console.log(`File downloaded successfully: ${finalFileName} (${fileData.size} bytes)`)
      return { success: true, data: fileData }

    } catch (error) {
      console.error("File download error:", error)
      return { success: false, error: error.message }
    }
  }

  // Update the displayQueue function to show message, caption, and link
  function displayQueue(queue) {
    queueContainer.style.display = "block"
    queueList.innerHTML = ""

    if (queue.length === 0) {
      queueList.innerHTML = "<p>No messages in queue</p>"
      return
    }

    queue.forEach((item, index) => {
      const queueItem = document.createElement("div")
      queueItem.className = "queue-item"
      queueItem.style.marginBottom = "15px"
      queueItem.style.padding = "10px"
      queueItem.style.border = "1px solid #ddd"
      queueItem.style.borderRadius = "5px"
      queueItem.style.backgroundColor = "#f9f9f9"

      // Debug the item structure
      console.log(`Queue item ${index}:`, item)

      // Fix the string handling in displayQueue function
      const phoneNumber = item.mobile || item.phone || item.number || item.to || "N/A"
      const message = (item.message || "No message").toString()
      const caption = (item.caption || "No caption").toString()
      const link = (item.link || "No media").toString()
      const status = (item.status || "Pending").toString()

      // Show multi key files information
      let multiFilesInfo = ""
      if (item.processedFiles && item.processedFiles.length > 0) {
        const localFiles = item.processedFiles.filter(f => f.isLocal).length
        const cloudFiles = item.processedFiles.filter(f => !f.isLocal).length
        multiFilesInfo = `<div style="margin-bottom: 5px;"><strong>📁 Multi Files:</strong> ${localFiles} local, ${cloudFiles} cloud files</div>`
      } else if (item.multiError) {
        multiFilesInfo = `<div style="margin-bottom: 5px;"><strong>⚠️ Multi Key Error:</strong> ${item.multiError}</div>`
      }

      queueItem.innerHTML = `
  <div style="margin-bottom: 5px;"><strong>📱 Phone:</strong> ${phoneNumber}</div>
  <div style="margin-bottom: 5px;"><strong>💬 Message:</strong> ${message.length > 100 ? message.substring(0, 100) + "..." : message}</div>
  <div style="margin-bottom: 5px;"><strong>📝 Caption:</strong> ${caption.length > 50 ? caption.substring(0, 50) + "..." : caption}</div>
  <div style="margin-bottom: 5px;"><strong>🔗 Media:</strong> ${link !== "No media" && link.trim() ? `<a href="${link}" target="_blank" style="color: #007bff; text-decoration: none;">View Media</a>` : "No media"}</div>
  ${multiFilesInfo}
  <div><strong>📊 Status:</strong> <span style="color: ${status === "0" ? "#ffc107" : status === "sent" ? "#28a745" : "#dc3545"};">${status === "0" ? "Pending" : status}</span></div>
`
      queueList.appendChild(queueItem)
    })
  }

  // Update the processBulkMessages function to handle the new message format
  async function processBulkMessages() {
    if (!isProcessing || currentIndex >= messageQueue.length) {
      isProcessing = false
      startBulkButton.style.display = "block"
      stopBulkButton.style.display = "none"
      progressContainer.style.display = "none"
      fetchQueueButton.disabled = false
      showStatus("Bulk sending completed!", "success")

      // Clear processing state
      saveToStorage({
        isProcessing: false,
        currentIndex: 0,
      })
      return
    }

    const currentMessage = messageQueue[currentIndex]
    updateProgress(currentIndex + 1, messageQueue.length)

    try {
      const tabs = await chrome.tabs.query({
        url: "https://web.whatsapp.com/*",
      })

      if (tabs.length === 0) {
        throw new Error("WhatsApp tab not found")
      }

      const tab = tabs[0]

      // Validate message data
      const phoneNumber = currentMessage.mobile || currentMessage.phone || currentMessage.number || currentMessage.to
      if (!phoneNumber) {
        throw new Error("No phone number found in message data")
      }

      // Check if we have any content to send
      const hasMessage = currentMessage.message && currentMessage.message.toString().trim()
      const hasCaption = currentMessage.caption && currentMessage.caption.toString().trim()
      const hasLink = currentMessage.link && currentMessage.link.toString().trim()
      const hasMultiFiles = currentMessage.processedFiles && currentMessage.processedFiles.length > 0

      if (!hasMessage && !hasCaption && !hasLink && !hasMultiFiles && apiBulkSelectedFiles.length === 0) {
        throw new Error("No content to send (no message, caption, link, multi files, or bulk files)")
      }

      console.log(`Processing bulk message ${currentIndex + 1}:`, {
        phone: phoneNumber,
        hasMessage,
        hasCaption,
        hasLink,
        hasMultiFiles,
        multiFilesCount: hasMultiFiles ? currentMessage.processedFiles.length : 0,
        bulkFilesCount: apiBulkSelectedFiles.length,
      })

      // Process multi key files if available
      let filesToSend = []

      if (hasMultiFiles) {
        console.log("Processing multi key files...")
        for (const multiFile of currentMessage.processedFiles) {
          try {
            if (multiFile.isLocal) {
              // Handle local file
              console.log(`\n--- POPUP: PROCESSING LOCAL FILE ---`)
              console.log(`Local file name: "${multiFile.localFileName}"`)
              console.log(`Local file caption: "${multiFile.caption || 'NONE'}"`)
              console.log(`Local file message: "${multiFile.message || 'NONE'}"`)
              console.log(`Local folder path configured: "${localFolderPathInput.value.trim() || 'NOT SET'}"`)

              // Check if we have a pre-selected file with this name
              const preSelectedFile = preSelectedLocalFiles.get(multiFile.localFileName)

              if (preSelectedFile) {
                console.log(`✅ Found pre-selected local file: ${multiFile.localFileName}`)

                try {
                  // Read the actual file data
                  const arrayBuffer = await preSelectedFile.arrayBuffer()
                  const uint8Array = new Uint8Array(arrayBuffer)

                  const localFileInfo = {
                    name: preSelectedFile.name,
                    type: preSelectedFile.type,
                    size: preSelectedFile.size,
                    data: Array.from(uint8Array), // Convert to array for serialization
                    lastModified: preSelectedFile.lastModified,
                    individualCaption: multiFile.caption || "",
                    individualMessage: multiFile.message || "",
                    isMultiKeyFile: true,
                    isLocalFile: false // Mark as false so it gets sent as actual file
                  }

                  filesToSend.push(localFileInfo)
                  console.log(`✅ Local file data loaded and added to send queue (${localFileInfo.size} bytes)`)

                } catch (fileError) {
                  console.error(`❌ Error reading pre-selected file: ${fileError.message}`)
                  // Fall back to text message
                  const localFileInfo = {
                    name: multiFile.localFileName,
                    type: "application/octet-stream",
                    size: 0,
                    data: [],
                    individualCaption: multiFile.caption || "",
                    individualMessage: multiFile.message || "",
                    isMultiKeyFile: true,
                    isLocalFile: true,
                    localFileName: multiFile.localFileName
                  }
                  filesToSend.push(localFileInfo)
                  console.warn(`⚠️ Falling back to text message due to file read error`)
                }
              } else {
                console.log(`❌ No pre-selected file found for: ${multiFile.localFileName}`)
                console.log(`Available pre-selected files:`, Array.from(preSelectedLocalFiles.keys()))

                // Create placeholder for text message
                const localFileInfo = {
                  name: multiFile.localFileName,
                  type: "application/octet-stream",
                  size: 0,
                  data: [],
                  individualCaption: multiFile.caption || "",
                  individualMessage: multiFile.message || "",
                  isMultiKeyFile: true,
                  isLocalFile: true,
                  localFileName: multiFile.localFileName
                }
                filesToSend.push(localFileInfo)
                console.warn(`⚠️ Local file not pre-selected - will be sent as text message`)
              }

            } else if (multiFile.link && multiFile.link.startsWith('http')) {
              // Download cloud file
              console.log(`Downloading cloud file: ${multiFile.link}`)
              try {
                const downloadResult = await downloadFileFromUrl(multiFile.link)
                if (downloadResult.success) {
                  const fileWithCaption = {
                    ...downloadResult.data,
                    // Use the individual file's caption and message from multi key
                    individualCaption: multiFile.caption || "",
                    individualMessage: multiFile.message || "",
                    // Mark this as a multi key file so content script knows to use individual captions
                    isMultiKeyFile: true
                  }
                  filesToSend.push(fileWithCaption)
                  console.log(`Cloud file downloaded successfully: ${downloadResult.data.name}`)
                } else {
                  console.error(`Failed to download cloud file: ${downloadResult.error}`)
                }
              } catch (downloadError) {
                console.error(`Error downloading cloud file: ${downloadError.message}`)
              }
            }
          } catch (fileError) {
            console.error(`Error processing multi file: ${fileError.message}`)
          }
        }
      }

      // Add bulk files to the files list
      if (apiBulkSelectedFiles.length > 0) {
        filesToSend.push(...apiBulkSelectedFiles)
      }

      // Prepare message data with files
      const messageDataWithFiles = {
        ...currentMessage,
        files: filesToSend.length > 0 ? filesToSend : null,
        // Clear the outer fields if we have multi key files to prevent content script from using them
        // Multi key files have their own individual captions and messages
        link: hasMultiFiles ? "" : currentMessage.link,
        message: hasMultiFiles ? "" : currentMessage.message,
        caption: hasMultiFiles ? "" : currentMessage.caption
      }

      console.log("Sending message with bulk files:", {
        phone: phoneNumber,
        filesCount: messageDataWithFiles.files ? messageDataWithFiles.files.length : 0,
      })

      // Send the complete message data including files
      await sendMessageViaContentScript(tab.id, messageDataWithFiles)

      // Update message status via API
      if (currentMessage.id) {
        const statusResult = await updateMessageStatus(currentMessage.id, "sent")
        console.log("Status update result:", statusResult)
      }

      console.log(`Bulk message sent successfully to ${phoneNumber}`)
      showStatus(
        `Sent ${currentIndex + 1}/${messageQueue.length} messages (with ${filesToSend.length} files from multi key + bulk)`,
        "success",
      )
    } catch (error) {
      console.error(`Error sending bulk message ${currentIndex + 1}:`, error)

      // Update message status to failed
      if (currentMessage.id) {
        await updateMessageStatus(currentMessage.id, "failed")
      }

      showStatus(`Error on message ${currentIndex + 1}: ${error.message}`, "error")
    }

    currentIndex++

    // Save current progress
    saveProcessingState()

    // Wait for the specified delay before processing next message
    const delay = Number.parseInt(delaySelect.value) * 1000
    setTimeout(() => {
      if (isProcessing) {
        processBulkMessages()
      }
    }, delay)
  }

  // Initialize the extension
  async function initialize() {
    console.log("Initializing WhatsApp Automation Extension...")

    // Load saved data first
    await loadSavedData()

    // Setup auto-save for form inputs
    setupAutoSave()

    // Add API bulk file setup to initialization
    setupApiBulkFileUpload()

    console.log("Extension initialized successfully!")
  }

  // Start initialization
  initialize()
})
